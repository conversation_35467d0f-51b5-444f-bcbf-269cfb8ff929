import { ChatService  } from '../services/ChatService.js';
import { LLMService  } from '../services/LLMService.js';
import { ResponseUtil  } from '../utils/response.js';
import { SUCCESS_MESSAGES, ERROR_MESSAGES  } from '../utils/constants.js';
import { asyncHandler  } from '../middleware/errorHandler.js';
import { EncryptionUtil  } from '../utils/encryption.js';
import logger from '../config/logger.js';
import { FileProcessingService  } from '../services/FileProcessingService.js';

import { Chat, ChatMessage  } from '../models/chat/index.js';

/**
 * Chat Controller
 * Handles chat operations including messaging, streaming, and file attachments
 */
class ChatController {
  /**
   * Send chat message (authenticated or guest)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendMessage = asyncHandler(async (req, res) => {
    const chatRequest = req.body;
    const user = req.user;
    const sessionId = req.sessionId || chatRequest.sessionId;
    const clientIP = req.clientIP || '127.0.0.1';

    if (user) {
      // Authenticated user chat
      // Ensure sessionId is set in chatRequest for conversation continuity
      if (sessionId && !chatRequest.sessionId) {
        chatRequest.sessionId = sessionId;
      }
      const result = await ChatService.processUserChat(user.userId, chatRequest);
      ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
    } else {
      // Guest user chat
      if (!sessionId) {
        // Generate new session ID for guest
        const newSessionId = EncryptionUtil.generateSessionId();
        chatRequest.sessionId = newSessionId;
      } else {
        chatRequest.sessionId = sessionId;
      }

      const result = await ChatService.processGuestChat(
        chatRequest.sessionId,
        chatRequest,
        clientIP
      );
      ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
    }
  });

  /**
   * Send streaming chat message (authenticated or guest)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendStreamingMessage = asyncHandler(async (req, res) => {
    const chatRequest = req.body;
    const user = req.user;
    const sessionId = req.sessionId || chatRequest.sessionId;
    const clientIP = req.clientIP || '127.0.0.1';

    try {
      if (user) {
        // Authenticated user chat
        // Ensure sessionId is set in chatRequest for conversation continuity
        if (sessionId && !chatRequest.sessionId) {
          chatRequest.sessionId = sessionId;
        }
        const result = await ChatService.processUserChatStreaming(user.userId, chatRequest, res);
        // Note: Response is handled by the streaming service, no need to send additional response
      } else {
        // Guest user chat
        if (!sessionId) {
          // Generate new session ID for guest
          const newSessionId = EncryptionUtil.generateSessionId();
          chatRequest.sessionId = newSessionId;
        } else {
          chatRequest.sessionId = sessionId;
        }

        const result = await ChatService.processGuestChatStreaming(
          chatRequest.sessionId,
          chatRequest,
          clientIP,
          res
        );
        // Note: Response is handled by the streaming service, no need to send additional response
      }
    } catch (error) {
      logger.error('Error in streaming chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Simplified streaming chat message handler
   * Removes complexity of session ID extraction, CSRF tokens, and IP tracking
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendSimpleStreamingMessage = asyncHandler(async (req, res) => {
    const chatRequest = req.body;
    const user = req.user;

    try {
      if (user) {
        // Authenticated user - simple flow
        const result = await ChatService.processSimpleUserChatStreaming(user.userId, chatRequest, res);
      } else {
        // Guest user - simple flow without IP tracking
        // Generate a guest ID for the session
        const guestId = req.sessionId || EncryptionUtil.generateSessionId();
        const result = await ChatService.processSimpleGuestChatStreaming(guestId, chatRequest, res);
      }
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in simple streaming chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Get chat history
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getChatHistory = asyncHandler(async (req, res) => {
    const { chatId } = req.params;
    const user = req.user;
    const sessionId = req.sessionId;

    if (!chatId) {
      ResponseUtil.badRequest(res, 'Chat ID is required');
      return;
    }

    let history;
    if (user) {
      // Authenticated user
      history = await ChatService.getChatHistory(chatId, user.userId);
    } else {
      // Guest user
      if (!sessionId) {
        ResponseUtil.unauthorized(res, 'Session ID is required for guest users');
        return;
      }
      history = await ChatService.getGuestChatHistory(chatId, sessionId);
    }

    ResponseUtil.success(res, 'Chat history retrieved successfully', history);
  });

  /**
   * Get user chats
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getUserChats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit) || 6;
    const offset = parseInt(req.query.offset) || 0;

    const chats = await ChatService.getUserChats(req.user.userId, limit, offset);
    
    ResponseUtil.success(res, 'Chats retrieved successfully', {
      chats,
      limit,
      offset,
    });
  });

  /**
   * Delete chat
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static deleteChat = asyncHandler(async (req, res) => {
    const { chatId } = req.params;
    const user = req.user;
    const sessionId = req.sessionId;

    if (!chatId) {
      ResponseUtil.badRequest(res, 'Chat ID is required');
      return;
    }

    if (user) {
      // Authenticated user
      await ChatService.deleteUserChat(chatId, user.userId);
    } else {
      // Guest user
      if (!sessionId) {
        ResponseUtil.unauthorized(res, 'Session ID is required for guest users');
        return;
      }
      await ChatService.deleteGuestChat(chatId, sessionId);
    }

    ResponseUtil.success(res, 'Chat deleted successfully');
  });

  /**
   * Update chat title
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updateChatTitle = asyncHandler(async (req, res) => {
    const { chatId } = req.params;
    const { title } = req.body;
    const user = req.user;
    const sessionId = req.sessionId;

    if (!chatId) {
      ResponseUtil.badRequest(res, 'Chat ID is required');
      return;
    }

    if (!title || title.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Title is required');
      return;
    }

    if (title.length > 255) {
      ResponseUtil.badRequest(res, 'Title is too long (max 255 characters)');
      return;
    }

    if (user) {
      // Authenticated user
      await ChatService.updateChatTitle(chatId, user.userId, title.trim());
    } else {
      // Guest user
      if (!sessionId) {
        ResponseUtil.unauthorized(res, 'Session ID is required for guest users');
        return;
      }
      await ChatService.updateGuestChatTitle(chatId, sessionId, title.trim());
    }

    ResponseUtil.success(res, 'Chat title updated successfully');
  });

  /**
   * Convert guest chat to user chat (when user logs in)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static convertGuestChat = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { sessionId } = req.body;

    if (!sessionId) {
      ResponseUtil.validationError(res, 'Session ID is required');
      return;
    }

    await ChatService.convertGuestChatToUser(sessionId, req.user.userId);
    ResponseUtil.success(res, 'Guest chat converted successfully');
  });

  /**
   * Get chat messages
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getChatMessages = asyncHandler(async (req, res) => {
    const { chatId } = req.params;
    const user = req.user;
    const sessionId = req.sessionId;

    if (!chatId) {
      ResponseUtil.badRequest(res, 'Chat ID is required');
      return;
    }

    let messages;
    if (user) {
      // Authenticated user
      messages = await ChatService.getChatMessages(chatId, user.userId);
    } else {
      // Guest user
      if (!sessionId) {
        ResponseUtil.unauthorized(res, 'Session ID is required for guest users');
        return;
      }
      messages = await ChatService.getGuestChatMessages(chatId, sessionId);
    }

    ResponseUtil.success(res, 'Chat messages retrieved successfully', messages);
  });

  /**
   * Get guest session info
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getGuestSessionInfo = asyncHandler(async (req, res) => {
    const sessionId = req.sessionId;

    if (!sessionId) {
      ResponseUtil.badRequest(res, 'Session ID is required');
      return;
    }

    const sessionInfo = await ChatService.getGuestSessionInfo(sessionId);
    ResponseUtil.success(res, 'Session info retrieved successfully', sessionInfo);
  });

  /**
   * Get available LLM models
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getAvailableModels = asyncHandler(async (req, res) => {
    const models = LLMService.getAvailableModels();
    ResponseUtil.success(res, 'Available models retrieved successfully', { models });
  });

  /**
   * Test model availability
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static testModel = asyncHandler(async (req, res) => {
    const { model } = req.body;

    if (!model) {
      ResponseUtil.badRequest(res, 'Model name is required');
      return;
    }

    try {
      const isAvailable = await LLMService.testModel(model);
      ResponseUtil.success(res, 'Model test completed', {
        model,
        available: isAvailable,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      ResponseUtil.success(res, 'Model test completed', {
        model,
        available: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });

  /**
   * Get chat statistics
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getChatStats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const stats = await ChatService.getUserChatStats(req.user.userId);
    ResponseUtil.success(res, 'Chat statistics retrieved successfully', stats);
  });

  /**
   * Search chats
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static searchChats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { q: searchTerm } = req.query;
    const limit = parseInt(req.query.limit) || 6;
    const offset = parseInt(req.query.offset) || 0;

    if (!searchTerm || typeof searchTerm !== 'string' || searchTerm.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Search term is required');
      return;
    }

    const chats = await ChatService.searchUserChats(
      req.user.userId,
      searchTerm.trim(),
      limit,
      offset
    );
    
    ResponseUtil.success(res, 'Chat search completed', {
      chats,
      searchTerm: searchTerm.trim(),
      limit,
      offset,
    });
  });

  /**
   * Export chat data
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static exportChat = asyncHandler(async (req, res) => {
    const { chatId } = req.params;
    const { format = 'json' } = req.query;
    const user = req.user;

    if (!chatId) {
      ResponseUtil.badRequest(res, 'Chat ID is required');
      return;
    }

    if (!user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // Get chat and verify ownership
    const chat = await Chat.findOne({
      where: { id: chatId, userId: user.userId, isGuest: false }
    });

    if (!chat) {
      ResponseUtil.notFound(res, 'Chat not found');
      return;
    }

    // Get all messages for this chat
    const messages = await ChatMessage.findAll({
      where: { chatId: chat.id },
      order: [['createdAt', 'ASC']]
    });

    const exportData = {
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt
      },
      messages: messages.map(msg => ({
        id: msg.id,
        message: msg.message,
        response: msg.response,
        llmModel: msg.llmModel,
        createdAt: msg.createdAt
      })),
      exportedAt: new Date().toISOString()
    };

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="chat-${chatId}.json"`);
      res.send(JSON.stringify(exportData, null, 2));
    } else {
      ResponseUtil.validationError(res, 'Unsupported export format');
    }
  });

  /**
   * Regenerate AI response for a specific message
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static regenerateResponse = asyncHandler(async (req, res) => {
    const regenerateRequest = req.body;
    const user = req.user;

    try {
      const result = await ChatService.regenerateResponse(
        regenerateRequest.messageId,
        user?.userId,
        regenerateRequest.llmModel,
        res
      );

      // For streaming responses, the response is handled by the service
      // For non-streaming, we would send the result here, but we're using streaming by default
    } catch (error) {
      logger.error('Error in regenerate response:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Send streaming chat message with file attachment support
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendStreamingMessageWithAttachment = asyncHandler(async (req, res) => {
    const chatRequest = req.body;
    const user = req.user;
    const file = req.file;

    try {
      let processedFile = undefined;

      // Process file if attached
      if (file) {
        const fileAttachment = {
          buffer: file.buffer,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        };

        processedFile = await FileProcessingService.processFile(fileAttachment);
        logger.info(`File processed successfully: ${file.originalname} (${file.mimetype})`);
      }

      if (user) {
        // Authenticated user - simple flow with file attachment
        const result = await ChatService.processSimpleUserChatStreamingWithAttachment(
          user.userId,
          chatRequest,
          res,
          processedFile
        );
      } else {
        // Guest user - simple flow with file attachment
        const result = await ChatService.processSimpleGuestChatStreamingWithAttachment(
          chatRequest,
          res,
          processedFile
        );
      }
    } catch (error) {
      logger.error('Error in streaming chat with attachment:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });
}

export { ChatController  };
