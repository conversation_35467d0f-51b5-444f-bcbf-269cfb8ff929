import * as fs from 'fs/promises';
import * as path from 'path';
import { PDFDocument } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import sharp from 'sharp';
import logger from '../config/logger';

export interface ProcessedFile {
  type: 'image' | 'document' | 'text';
  content: string | Array<{ type: 'image_url'; image_url: { url: string } }>;
  metadata: {
    originalName: string;
    mimeType: string;
    size: number;
    processedAt: Date;
  };
  filePath?: string; // Path where the file is stored on disk
}

export interface FileAttachment {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

export class FileProcessingService {
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  private static readonly SUPPORTED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/plain',
    'text/markdown',
    'application/json'
  ];

  /**
   * Save file to uploads directory and return the file path
   */
  private static async saveFileToUploads(file: FileAttachment): Promise<string> {
    const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');

    // Ensure uploads directory exists
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const fileExtension = path.extname(file.originalname);
    const fileName = `${timestamp}_${randomSuffix}${fileExtension}`;
    const filePath = path.join(uploadsDir, fileName);

    // Save file to disk
    await fs.writeFile(filePath, file.buffer);

    // Return relative path from project root
    return path.join('uploads', 'chatAttachments', fileName);
  }

  /**
   * Validate file before processing
   */
  static validateFile(file: FileAttachment): void {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    // Check if file type is supported
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype);
    const isDocument = this.SUPPORTED_DOCUMENT_TYPES.includes(file.mimetype);
    const isVideo = file.mimetype.startsWith('video/');

    if (isVideo) {
      throw new Error('Video files are not supported');
    }

    if (!isImage && !isDocument) {
      throw new Error(`Unsupported file type: ${file.mimetype}`);
    }

    logger.info(`File validation passed: ${file.originalname} (${file.mimetype}, ${file.size} bytes)`);
  }

  /**
   * Process file based on its type
   */
  static async processFile(file: FileAttachment): Promise<ProcessedFile> {
    this.validateFile(file);

    // Save file to uploads directory
    const filePath = await this.saveFileToUploads(file);

    const metadata = {
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      processedAt: new Date()
    };

    try {
      // Process images
      if (this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype)) {
        return await this.processImage(file, metadata, filePath);
      }

      // Process PDF
      if (file.mimetype === 'application/pdf') {
        return await this.processPDF(file, metadata, filePath);
      }

      // Process Word documents
      if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        return await this.processWordDocument(file, metadata, filePath);
      }

      // Process Excel files
      if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        return await this.processExcelFile(file, metadata, filePath);
      }

      // Process text files
      if (file.mimetype.startsWith('text/') || file.mimetype === 'application/json') {
        return await this.processTextFile(file, metadata, filePath);
      }

      throw new Error(`Unsupported file type: ${file.mimetype}`);
    } catch (error) {
      logger.error(`Error processing file ${file.originalname}:`, error);
      throw new Error(`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process image files
   */
  private static async processImage(
    file: FileAttachment,
    metadata: ProcessedFile['metadata'],
    filePath: string
  ): Promise<ProcessedFile> {
    try {
      // Optimize image if needed (compress large images)
      let processedBuffer = file.buffer;
      
      if (file.size > 1024 * 1024) { // If larger than 1MB, compress
        processedBuffer = await sharp(file.buffer)
          .jpeg({ quality: 85 })
          .toBuffer();
        
        logger.info(`Compressed image ${file.originalname} from ${file.size} to ${processedBuffer.length} bytes`);
      }

      const base64Image = processedBuffer.toString('base64');
      const mimeType = file.mimetype === 'image/jpg' ? 'image/jpeg' : file.mimetype;
      
      return {
        type: 'image',
        content: [{
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${base64Image}`
          }
        }],
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing image ${file.originalname}:`, error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF files by extracting text content
   * Note: For now we extract text. In the future, we can add image conversion
   */
  private static async processPDF(
    file: FileAttachment,
    metadata: ProcessedFile['metadata'],
    filePath: string
  ): Promise<ProcessedFile> {
    try {
      // Load PDF document
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      // For now, we'll return a text representation
      // In the future, we can implement PDF to image conversion using a different approach
      const textContent = `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nThis PDF has been uploaded and saved. The LLM can analyze the document structure and content.`;

      logger.info(`PDF processed: ${file.originalname} with ${pageCount} pages`);

      return {
        type: 'document',
        content: textContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing PDF ${file.originalname}:`, error);
      throw new Error(`Failed to process PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Word documents
   */
  private static async processWordDocument(
    file: FileAttachment,
    metadata: ProcessedFile['metadata'],
    filePath: string
  ): Promise<ProcessedFile> {
    try {
      const result = await mammoth.convertToHtml({ buffer: file.buffer });

      if (result.messages.length > 0) {
        logger.warn(`Word document conversion warnings for ${file.originalname}:`, result.messages);
      }

      // Convert HTML to basic markdown-like format
      let markdownContent = result.value
        .replace(/<h([1-6])>/g, (match, level) => '#'.repeat(parseInt(level)) + ' ')
        .replace(/<\/h[1-6]>/g, '\n\n')
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '\n\n')
        .replace(/<strong>/g, '**')
        .replace(/<\/strong>/g, '**')
        .replace(/<em>/g, '*')
        .replace(/<\/em>/g, '*')
        .replace(/<br\s*\/?>/g, '\n')
        .replace(/<[^>]*>/g, '') // Remove remaining HTML tags
        .replace(/\n{3,}/g, '\n\n') // Clean up excessive newlines
        .trim();

      return {
        type: 'document',
        content: markdownContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Word document ${file.originalname}:`, error);
      throw new Error(`Failed to process Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Excel files
   */
  private static async processExcelFile(
    file: FileAttachment,
    metadata: ProcessedFile['metadata'],
    filePath: string
  ): Promise<ProcessedFile> {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      let markdownContent = '';

      workbook.SheetNames.forEach((sheetName, index) => {
        const worksheet = workbook.Sheets[sheetName];
        const csvData = XLSX.utils.sheet_to_csv(worksheet);
        
        markdownContent += `## Sheet: ${sheetName}\n\n`;
        
        // Convert CSV to markdown table
        const lines = csvData.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
          // Header row
          const headers = lines[0].split(',').map(h => h.trim());
          markdownContent += `| ${headers.join(' | ')} |\n`;
          markdownContent += `| ${headers.map(() => '---').join(' | ')} |\n`;
          
          // Data rows (limit to first 50 rows to avoid token limits)
          const dataRows = lines.slice(1, 51);
          dataRows.forEach(line => {
            const cells = line.split(',').map(c => c.trim());
            markdownContent += `| ${cells.join(' | ')} |\n`;
          });
          
          if (lines.length > 51) {
            markdownContent += `\n*... and ${lines.length - 51} more rows*\n`;
          }
        }
        
        markdownContent += '\n';
      });

      return {
        type: 'document',
        content: markdownContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Excel file ${file.originalname}:`, error);
      throw new Error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process text files
   */
  private static async processTextFile(
    file: FileAttachment,
    metadata: ProcessedFile['metadata'],
    filePath: string
  ): Promise<ProcessedFile> {
    try {
      const content = file.buffer.toString('utf-8');
      
      return {
        type: 'text',
        content,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing text file ${file.originalname}:`, error);
      throw new Error(`Failed to process text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get system prompt addition based on file type
   */
  static getSystemPromptAddition(processedFile: ProcessedFile): string {
    const { type, metadata } = processedFile;
    
    switch (type) {
      case 'image':
        return `\n\nThe user has attached an image file (${metadata.originalName}). Please analyze the image content and respond accordingly.`;
      
      case 'document':
        if (metadata.mimeType === 'application/pdf') {
          return `\n\nThe user has attached a PDF document (${metadata.originalName}) which has been converted to images. Please analyze the document content and respond accordingly.`;
        } else if (metadata.mimeType.includes('word')) {
          return `\n\nThe user has attached a Word document (${metadata.originalName}) with the following content converted to markdown format. Please analyze the document and respond accordingly.`;
        } else if (metadata.mimeType.includes('sheet')) {
          return `\n\nThe user has attached an Excel spreadsheet (${metadata.originalName}) with the following data converted to markdown table format. Please analyze the data and respond accordingly.`;
        }
        return `\n\nThe user has attached a document (${metadata.originalName}). Please analyze the content and respond accordingly.`;
      
      case 'text':
        return `\n\nThe user has attached a text file (${metadata.originalName}) with the following content. Please analyze the text and respond accordingly.`;
      
      default:
        return `\n\nThe user has attached a file (${metadata.originalName}). Please analyze the content and respond accordingly.`;
    }
  }
}
