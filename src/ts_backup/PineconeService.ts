import { Pinecone } from '@pinecone-database/pinecone';
import logger from '../config/logger';

export class PineconeService {
  private static client: Pinecone;
  private static index: any;

  /**
   * Initialize Pinecone client
   */
  static async initialize(): Promise<void> {
    try {
      if (!process.env.PINECONE_API_KEY) {
        logger.warn('Pinecone API key not found. Pinecone service will be disabled.');
        return;
      }

      this.client = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY,
      });

      const indexName = process.env.PINECONE_INDEX_NAME || 'theinfini-ai-chat';
      this.index = this.client.index(indexName);

      logger.info('Pinecone service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Pinecone service:', error);
      throw error;
    }
  }

  /**
   * Check if Pinecone is available
   */
  static isAvailable(): boolean {
    return !!this.client && !!this.index;
  }

  /**
   * Generate embedding for text using OpenAI
   */
  static async generateEmbedding(text: string): Promise<number[]> {
    try {
      // For now, we'll use a simple text-to-vector conversion
      // In production, you'd want to use OpenAI's embedding API
      const { createHash } = await import('crypto');
      const hash = createHash('sha256').update(text).digest('hex');
      
      // Convert hash to a 1536-dimensional vector (OpenAI embedding size)
      const vector: number[] = [];
      for (let i = 0; i < 1536; i++) {
        const charCode = hash.charCodeAt(i % hash.length);
        vector.push((charCode / 255) * 2 - 1); // Normalize to [-1, 1]
      }
      
      return vector;
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  /**
   * Store chat message in Pinecone
   */
  static async storeMessage(
    projectId: string,
    threadId: string,
    messageId: string,
    message: string,
    response: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping message storage');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      const combinedText = `User: ${message}\nAssistant: ${response}`;
      const embedding = await this.generateEmbedding(combinedText);

      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: embedding,
          metadata: {
            projectId,
            threadId,
            message,
            response,
            timestamp: new Date().toISOString(),
            ...metadata,
          },
        },
      ]);

      logger.debug(`Message stored in Pinecone: ${messageId} in namespace ${namespace}`);
    } catch (error) {
      logger.error('Error storing message in Pinecone:', error);
      // Don't throw error to avoid breaking chat functionality
    }
  }

  /**
   * Search for similar messages in project context
   */
  static async searchSimilarMessages(
    projectId: string,
    threadId: string,
    query: string,
    topK: number = 5
  ): Promise<any[]> {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = `${projectId}_${threadId}`;
      const queryEmbedding = await this.generateEmbedding(query);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      return searchResults.matches || [];
    } catch (error) {
      logger.error('Error searching similar messages in Pinecone:', error);
      return [];
    }
  }

  /**
   * Delete all messages for a thread
   */
  static async deleteThreadMessages(projectId: string, threadId: string): Promise<void> {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping deletion');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      
      // Delete all vectors in the namespace
      await this.index.namespace(namespace).deleteAll();

      logger.debug(`Deleted all messages for thread ${threadId} in project ${projectId}`);
    } catch (error) {
      logger.error('Error deleting thread messages from Pinecone:', error);
    }
  }

  /**
   * Delete all messages for a project
   */
  static async deleteProjectMessages(projectId: string): Promise<void> {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping deletion');
        return;
      }

      // Get all namespaces for this project and delete them
      // Note: This is a simplified approach. In production, you might want to
      // maintain a list of namespaces per project for efficient cleanup
      logger.debug(`Deleting all messages for project ${projectId}`);
    } catch (error) {
      logger.error('Error deleting project messages from Pinecone:', error);
    }
  }

  /**
   * Get conversation context from Pinecone for RAG
   */
  static async getConversationContext(
    projectId: string,
    threadId: string,
    currentMessage: string,
    maxContext: number = 3
  ): Promise<string[]> {
    try {
      const similarMessages = await this.searchSimilarMessages(
        projectId,
        threadId,
        currentMessage,
        maxContext
      );

      return similarMessages.map(match => {
        const metadata = match.metadata;
        return `Previous conversation:\nUser: ${metadata.message}\nAssistant: ${metadata.response}`;
      });
    } catch (error) {
      logger.error('Error getting conversation context from Pinecone:', error);
      return [];
    }
  }
}
