import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { validate, validationSchemas, validateQuery } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import Joi from 'joi';

const router = Router();

// All user routes require authentication
router.use(authenticateToken);

// Profile management
router.get('/profile',
  UserController.getProfile
);

router.put('/profile',
  validate(Joi.object({
    firstName: Joi.string().max(100).optional(),
    lastName: Joi.string().max(100).optional(),
    email: Joi.string().email().optional(),
    mobile: Joi.string().pattern(/^[+]?[1-9][\d]{7,14}$/).optional(),
    profilePicture: Joi.string().uri().optional(),
  })),
  UserController.updateProfile
);

// User statistics and activity
router.get('/stats',
  UserController.getUserStats
);

router.get('/activity',
  validateQuery(validationSchemas.pagination),
  UserController.getActivityLog
);

// User preferences
router.get('/preferences',
  UserController.getPreferences
);

router.put('/preferences',
  validate(Joi.object({
    defaultLLMModel: Joi.string().optional(),
    theme: Joi.string().valid('light', 'dark').optional(),
    language: Joi.string().length(2).optional(),
    notifications: Joi.object({
      email: Joi.boolean().optional(),
      push: Joi.boolean().optional(),
    }).optional(),
  })),
  UserController.updatePreferences
);

// Profile details management
router.put('/profile/details',
  validate(Joi.object({
    firstName: Joi.string().max(100).optional(),
    lastName: Joi.string().max(100).optional(),
    profilePicture: Joi.string().uri().optional(),
  })),
  UserController.updateProfileDetails
);

// Password management
router.put('/password',
  validate(Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(8).required(),
  })),
  UserController.changePassword
);

// Credit management
router.get('/credits',
  UserController.getCreditSummary
);

router.get('/credits/transactions',
  validateQuery(validationSchemas.pagination),
  UserController.getCreditTransactions
);

// Plan management
router.put('/plan',
  validate(Joi.object({
    plan: Joi.string().valid('FREE', 'PREMIUM', 'ENTERPRISE').required(),
  })),
  UserController.updatePlan
);

// Data export
router.get('/export',
  UserController.exportUserData
);

// Account management
router.post('/deactivate',
  UserController.deactivateAccount
);

router.delete('/delete',
  validate(Joi.object({
    password: Joi.string().optional(),
  })),
  UserController.deleteAccount
);

export default router;
