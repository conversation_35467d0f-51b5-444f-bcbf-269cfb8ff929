import { UserOTP } from '../models/user';
import { EncryptionUtil } from '../utils/encryption';
import { VALIDATION_RULES } from '../utils/constants';
import logger from '../config/logger';

export class OTPService {
  /**
   * Generate and store OTP for user
   */
  static async generateOTP(userId: string): Promise<string> {
    try {
      // Clean up any existing unused OTPs for this user
      await this.cleanupUserOTPs(userId);

      // Generate new OTP
      const otp = EncryptionUtil.generateOTP(VALIDATION_RULES.OTP_LENGTH);
      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');

      // Store OTP in database
      await UserOTP.createOTP(userId, otp, expiryMinutes);

      logger.info(`OTP generated for user: ${userId}`);
      return otp;
    } catch (error) {
      logger.error('Error generating OTP:', error);
      throw new Error('Failed to generate OTP');
    }
  }

  /**
   * Verify OTP for user
   */
  static async verifyOTP(userId: string, otp: string): Promise<boolean> {
    try {
      // Find valid OTP
      const otpRecord = await UserOTP.findValidOTP(userId, otp);

      if (!otpRecord) {
        logger.warn(`Invalid OTP attempt for user: ${userId}`);
        return false;
      }

      // Check if OTP is expired
      if (otpRecord.isExpired()) {
        logger.warn(`Expired OTP attempt for user: ${userId}`);
        await otpRecord.destroy(); // Clean up expired OTP
        return false;
      }

      // Mark OTP as used and delete it
      await otpRecord.destroy();
      
      logger.info(`OTP verified successfully for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error verifying OTP:', error);
      throw new Error('Failed to verify OTP');
    }
  }

  /**
   * Clean up expired OTPs
   */
  static async cleanupExpiredOTPs(): Promise<number> {
    try {
      const deletedCount = await UserOTP.cleanupExpiredOTPs();
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} expired OTPs`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up expired OTPs:', error);
      return 0;
    }
  }

  /**
   * Clean up used OTPs
   */
  static async cleanupUsedOTPs(): Promise<number> {
    try {
      const deletedCount = await UserOTP.cleanupUsedOTPs();
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} used OTPs`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up used OTPs:', error);
      return 0;
    }
  }

  /**
   * Clean up all OTPs for a specific user
   */
  static async cleanupUserOTPs(userId: string): Promise<number> {
    try {
      const deletedCount = await UserOTP.destroy({
        where: { userId },
      });
      
      if (deletedCount > 0) {
        logger.debug(`Cleaned up ${deletedCount} OTPs for user: ${userId}`);
      }
      
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up user OTPs:', error);
      return 0;
    }
  }

  /**
   * Check if user has pending OTP
   */
  static async hasPendingOTP(userId: string): Promise<boolean> {
    try {
      const otpRecord = await UserOTP.findOne({
        where: {
          userId,
          isUsed: false,
        },
      });

      if (!otpRecord) {
        return false;
      }

      // Check if OTP is expired
      if (otpRecord.isExpired()) {
        await otpRecord.destroy(); // Clean up expired OTP
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error checking pending OTP:', error);
      return false;
    }
  }

  /**
   * Get OTP expiry time for user
   */
  static async getOTPExpiryTime(userId: string): Promise<Date | null> {
    try {
      const otpRecord = await UserOTP.findOne({
        where: {
          userId,
          isUsed: false,
        },
        order: [['createdAt', 'DESC']],
      });

      if (!otpRecord || otpRecord.isExpired()) {
        return null;
      }

      return otpRecord.expiresAt;
    } catch (error) {
      logger.error('Error getting OTP expiry time:', error);
      return null;
    }
  }

  /**
   * Send OTP via SMS/Email (placeholder for actual implementation)
   */
  static async sendOTP(identifier: string, otp: string): Promise<boolean> {
    try {
      // This is a placeholder implementation
      // In a real application, you would integrate with SMS/Email services
      // like Twilio, SendGrid, AWS SES, etc.
      
      const { VALIDATION_RULES } = await import('../utils/constants');
      const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
      
      if (isEmail) {
        // Send email OTP
        logger.info(`Sending OTP via email to: ${identifier}`);
        // await emailService.sendOTP(identifier, otp);
      } else {
        // Send SMS OTP
        logger.info(`Sending OTP via SMS to: ${identifier}`);
        // await smsService.sendOTP(identifier, otp);
      }

      // For development, log the OTP
      if (process.env.NODE_ENV === 'development') {
        logger.info(`OTP for ${identifier}: ${otp}`);
      }

      return true;
    } catch (error) {
      logger.error('Error sending OTP:', error);
      return false;
    }
  }

  /**
   * Schedule cleanup job for expired OTPs
   */
  static scheduleCleanup(): void {
    // Run cleanup every hour
    setInterval(async () => {
      await this.cleanupExpiredOTPs();
      await this.cleanupUsedOTPs();
    }, 60 * 60 * 1000); // 1 hour

    logger.info('OTP cleanup scheduler started');
  }
}
