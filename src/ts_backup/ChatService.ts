import { Chat, <PERSON>t<PERSON><PERSON><PERSON>, Chat<PERSON>hread, GuestSession } from '../models/chat';
import { LLMService } from './LLMService';
import { CreditService } from './CreditService';
import { EncryptionUtil } from '../utils/encryption';
import { VALIDATION_RULES, ERROR_MESSAGES, CREDIT_SYSTEM } from '../utils/constants';
import { ChatRequest, ChatResponse, SimpleChatRequest, SimpleChatResponse, RegenerateRequest } from '../types';
import logger from '../config/logger';
import { Response } from 'express';
import { ProcessedFile } from './FileProcessingService';

export class ChatService {
  /**
   * Process chat message for authenticated user
   */
  static async processUserChat(
    userId: string,
    chatRequest: ChatRequest
  ): Promise<ChatResponse> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat: Chat;

      if (sessionId) {
        // Find existing chat
        const existingChat = await Chat.findBySessionId(sessionId);

        if (existingChat) {
          // Chat exists - check ownership and handle guest conversion
          if (existingChat.isGuest) {
            // Convert guest chat to user chat
            await existingChat.convertToUserChat(userId);
            chat = existingChat;
            logger.info(`Converted guest chat to user chat: ${sessionId} -> ${userId}`);
          } else if (existingChat.userId === userId) {
            // User's existing chat
            chat = existingChat;
          } else {
            // Chat belongs to different user
            throw new Error(ERROR_MESSAGES.CHAT_NOT_FOUND);
          }
        } else {
          // No existing chat with this session ID - create new one
          chat = await Chat.createChat({
            userId,
            sessionId,
            isGuest: false,
          });
          logger.info(`Created new chat for user with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await Chat.createChat({
          userId,
          sessionId: newSessionId,
          isGuest: false,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Deduct 1 credit for the chat message
      const creditDeducted = await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
        // Note: We don't fail the request here since the message was already processed
      }

      // Update chat title if it's the first message
      if (!chat.title) {
        const title = this.generateChatTitle(message);
        await chat.updateTitle(title);
      }

      // Update chat timestamp
      chat.changed('updatedAt', true);
      await chat.save();

      logger.info(`Chat message processed for user: ${userId}, chat: ${chat.id}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
        isGuest: false,
      };
    } catch (error) {
      logger.error('Error processing user chat:', error);
      throw error;
    }
  }

  /**
   * Process chat message for guest user
   */
  static async processGuestChat(
    sessionId: string,
    chatRequest: ChatRequest,
    ipAddress: string
  ): Promise<ChatResponse> {
    try {
      const { message, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Find or create guest session
      const [guestSession] = await GuestSession.findOrCreateSession(sessionId, ipAddress);

      // Check guest chat limit
      const guestLimit = parseInt(process.env.GUEST_CHAT_LIMIT || '5');
      if (guestSession.hasReachedLimit(guestLimit)) {
        throw new Error(ERROR_MESSAGES.GUEST_LIMIT_EXCEEDED);
      }

      // Find or create guest chat
      let chat = await Chat.findBySessionId(sessionId);
      if (!chat) {
        chat = await Chat.createChat({
          sessionId,
          isGuest: true,
        });
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getGuestSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-4.1-mini',
      });

      // Increment guest session message count
      await guestSession.incrementMessageCount();

      // Update chat title if it's the first message
      if (!chat.title) {
        const title = this.generateChatTitle(message);
        await chat.updateTitle(title);
      }

      const remainingChats = guestSession.getRemainingMessages(guestLimit);

      logger.info(`Guest chat message processed: ${sessionId}, remaining: ${remainingChats}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
        isGuest: true,
        remainingGuestChats: remainingChats,
      };
    } catch (error) {
      logger.error('Error processing guest chat:', error);
      throw error;
    }
  }

  /**
   * Process streaming chat message for authenticated user
   */
  static async processUserChatStreaming(
    userId: string,
    chatRequest: ChatRequest,
    res: Response
  ): Promise<{ sessionId: string; messageId: string; isGuest: boolean }> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat: Chat;

      if (sessionId) {
        // Find existing chat
        const existingChat = await Chat.findBySessionId(sessionId);

        if (existingChat) {
          // Chat exists - check ownership and handle guest conversion
          if (existingChat.isGuest) {
            // Convert guest chat to user chat
            await existingChat.convertToUserChat(userId);
            chat = existingChat;
            logger.info(`Converted guest chat to user chat: ${sessionId} -> ${userId}`);
          } else if (existingChat.userId === userId) {
            // User's existing chat
            chat = existingChat;
          } else {
            // Chat belongs to different user
            throw new Error(ERROR_MESSAGES.CHAT_NOT_FOUND);
          }
        } else {
          // No existing chat with this session ID - create new one
          chat = await Chat.createChat({
            userId,
            sessionId,
            isGuest: false,
          });
          logger.info(`Created new chat for user with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await Chat.createChat({
          userId,
          sessionId: newSessionId,
          isGuest: false,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate streaming response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chat.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: false,
        chatId: chat.id
      };

      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response
          const chatMessage = await ChatMessage.createMessage({
            chatId: chat.id,
            message,
            response: fullResponse,
            llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
          });

          // Return updated metadata with messageId
          return {
            messageId: chatMessage.id
          };
        }
      );

      // Deduct 1 credit for the chat message
      const creditDeducted = await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      // Update chat title if it's the first message
      if (!chat.title) {
        const title = this.generateChatTitle(message);
        await chat.updateTitle(title);
      }

      // Update chat timestamp
      chat.changed('updatedAt', true);
      await chat.save();

      logger.info(`Streaming chat message processed for user: ${userId}, chat: ${chat.id}`);

      return {
        sessionId: chat.sessionId,
        messageId: 'will-be-in-stream', // Actual messageId is sent in the stream
        isGuest: false,
      };
    } catch (error) {
      logger.error('Error processing user streaming chat:', error);
      throw error;
    }
  }

  /**
   * Process streaming chat message for guest user
   */
  static async processGuestChatStreaming(
    sessionId: string,
    chatRequest: ChatRequest,
    ipAddress: string,
    res: Response
  ): Promise<{ sessionId: string; messageId: string; isGuest: boolean; remainingGuestChats: number }> {
    try {
      const { message, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Find or create guest session
      const [guestSession] = await GuestSession.findOrCreateSession(sessionId, ipAddress);

      // Check guest chat limit
      const guestLimit = parseInt(process.env.GUEST_CHAT_LIMIT || '5');
      if (guestSession.hasReachedLimit(guestLimit)) {
        throw new Error(ERROR_MESSAGES.GUEST_LIMIT_EXCEEDED);
      }

      // Find or create guest chat
      let chat = await Chat.findBySessionId(sessionId);
      if (!chat) {
        chat = await Chat.createChat({
          sessionId,
          isGuest: true,
        });
      }

      // Generate streaming response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chat.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: true,
        chatId: chat.id,
        remainingGuestChats: guestSession.getRemainingMessages(guestLimit)
      };

      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel,
        this.getGuestSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response
          const chatMessage = await ChatMessage.createMessage({
            chatId: chat.id,
            message,
            response: fullResponse,
            llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-4.1-mini',
          });

          // Return updated metadata with messageId
          return {
            messageId: chatMessage.id
          };
        }
      );

      // Increment guest session message count
      await guestSession.incrementMessageCount();

      // Update chat title if it's the first message
      if (!chat.title) {
        const title = this.generateChatTitle(message);
        await chat.updateTitle(title);
      }

      const remainingChats = guestSession.getRemainingMessages(guestLimit);

      logger.info(`Guest streaming chat message processed: ${sessionId}, remaining: ${remainingChats}`);

      return {
        sessionId: chat.sessionId,
        messageId: 'will-be-in-stream', // Actual messageId is sent in the stream
        isGuest: true,
        remainingGuestChats: remainingChats,
      };
    } catch (error) {
      logger.error('Error processing guest streaming chat:', error);
      throw error;
    }
  }

  /**
   * Simplified streaming chat for authenticated users
   * Uses ChatThread model for proper thread management
   */
  static async processSimpleUserChatStreaming(
    userId: string,
    chatRequest: SimpleChatRequest,
    res: Response
  ): Promise<SimpleChatResponse> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chatThread: ChatThread;

      if (sessionId) {
        // Find existing thread by sessionId
        const existingThread = await ChatThread.findBySessionId(sessionId);
        if (existingThread && existingThread.userId === userId) {
          chatThread = existingThread;
        } else {
          // Create new thread with provided sessionId and initial name
          const initialName = ChatThread.prototype.generateThreadName(message);
          chatThread = await ChatThread.createThread({
            userId,
            sessionId,
            name: initialName,
            isGuest: false,
          });
        }
      } else {
        // Create new thread with generated session ID and initial name
        const newSessionId = EncryptionUtil.generateSessionId();
        const initialName = ChatThread.prototype.generateThreadName(message);
        chatThread = await ChatThread.createThread({
          userId,
          sessionId: newSessionId,
          name: initialName,
          isGuest: false,
        });
      }

      // Generate streaming response using LLM
      const conversationHistory = await this.getChatHistory(chatThread.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chatThread.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: false,
        userId: userId,
        threadId: chatThread.id
      };

      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel || 'gpt-3.5-turbo',
        this.getSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response
          const chatMessage = await ChatMessage.createMessage({
            chatId: chatThread.id, // Using thread ID as chat ID
            message,
            response: fullResponse,
            llmModel: llmModel || 'gpt-3.5-turbo',
          });

          // Deduct 1 credit for the chat message
          await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);

          // Update thread timestamp
          chatThread.changed('updatedAt', true);
          await chatThread.save();

          return {
            messageId: chatMessage.id
          };
        }
      );

      logger.info(`Simple streaming chat processed for user: ${userId}, thread: ${chatThread.id}`);

      return {
        sessionId: chatThread.sessionId,
        messageId: 'will-be-in-stream',
        isGuest: false,
        userId: userId,
      };
    } catch (error) {
      logger.error('Error processing simple user streaming chat:', error);
      throw error;
    }
  }

  /**
   * Simplified streaming chat for guest users
   * Uses ChatThread model for proper thread management
   */
  static async processSimpleGuestChatStreaming(
    chatRequest: SimpleChatRequest,
    res: Response
  ): Promise<SimpleChatResponse> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Generate session ID if not provided
      const finalSessionId = sessionId || EncryptionUtil.generateSessionId();

      // Find or create guest thread
      let chatThread = await ChatThread.findBySessionId(finalSessionId);
      if (!chatThread) {
        // Create new guest thread with initial name
        const initialName = ChatThread.prototype.generateThreadName(message);
        chatThread = await ChatThread.createThread({
          sessionId: finalSessionId,
          name: initialName,
          isGuest: true,
        });
      }

      // Generate streaming response using LLM
      const conversationHistory = await this.getChatHistory(chatThread.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chatThread.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: true,
        threadId: chatThread.id
      };

      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel || 'gpt-3.5-turbo',
        this.getGuestSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response
          const chatMessage = await ChatMessage.createMessage({
            chatId: chatThread.id, // Using thread ID as chat ID
            message,
            response: fullResponse,
            llmModel: llmModel || 'gpt-3.5-turbo',
          });

          return {
            messageId: chatMessage.id
          };
        }
      );

      logger.info(`Simple guest streaming chat processed: ${finalSessionId}`);

      return {
        sessionId: chatThread.sessionId,
        messageId: 'will-be-in-stream',
        isGuest: true,
      };
    } catch (error) {
      logger.error('Error processing simple guest streaming chat:', error);
      throw error;
    }
  }

  /**
   * Get chat history for context
   */
  private static async getChatHistory(chatId: string): Promise<Array<{ role: 'user' | 'assistant'; content: string }>> {
    try {
      const messages = await ChatMessage.findChatMessages(chatId, 10); // Last 10 messages for context

      return messages.map(msg => [
        { role: 'user' as const, content: msg.message },
        { role: 'assistant' as const, content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting chat history:', error);
      return [];
    }
  }

  /**
   * Get chat history up to a specific message for regeneration
   */
  private static async getChatHistoryUpToMessage(
    chatId: string,
    targetMessageId: string
  ): Promise<Array<{ role: 'user' | 'assistant'; content: string }>> {
    try {
      // Get all messages in the chat
      const allMessages = await ChatMessage.findAll({
        where: {
          chatId,
          isRegenerated: false, // Only include original messages, not regenerated ones
        },
        order: [['createdAt', 'ASC']],
      });

      // Find the target message and get all messages before it
      const targetIndex = allMessages.findIndex(msg => msg.id === targetMessageId);
      if (targetIndex === -1) {
        throw new Error('Target message not found in chat history');
      }

      // Get messages up to (but not including) the target message
      const historyMessages = allMessages.slice(0, targetIndex);

      return historyMessages.map(msg => [
        { role: 'user' as const, content: msg.message },
        { role: 'assistant' as const, content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting chat history up to message:', error);
      return [];
    }
  }

  /**
   * Get user chats
   */
  static async getUserChats(userId: string, limit: number = 20, offset: number = 0): Promise<Chat[]> {
    try {
      return Chat.findUserChats(userId, limit, offset);
    } catch (error) {
      logger.error('Error getting user chats:', error);
      throw error;
    }
  }

  /**
   * Get chat messages
   */
  static async getChatMessages(
    chatId: string,
    userId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ChatMessage[]> {
    try {
      // Verify chat ownership if user is provided
      if (userId) {
        const chat = await Chat.findByPk(chatId);
        if (!chat || chat.userId !== userId) {
          throw new Error(ERROR_MESSAGES.CHAT_NOT_FOUND);
        }
      }

      return ChatMessage.findChatMessages(chatId, limit, offset);
    } catch (error) {
      logger.error('Error getting chat messages:', error);
      throw error;
    }
  }

  /**
   * Delete chat
   */
  static async deleteChat(chatId: string, userId: string): Promise<void> {
    try {
      const chat = await Chat.findByPk(chatId);
      if (!chat || chat.userId !== userId) {
        throw new Error(ERROR_MESSAGES.CHAT_NOT_FOUND);
      }

      // Delete all messages first
      await ChatMessage.deleteChatMessages(chatId);

      // Delete chat
      await chat.destroy();

      logger.info(`Chat deleted: ${chatId} by user: ${userId}`);
    } catch (error) {
      logger.error('Error deleting chat:', error);
      throw error;
    }
  }

  /**
   * Convert guest chat to user chat
   */
  static async convertGuestChatToUser(sessionId: string, userId: string): Promise<void> {
    try {
      const chat = await Chat.findBySessionId(sessionId);
      if (!chat || !chat.isGuest) {
        throw new Error('Guest chat not found');
      }

      await chat.convertToUserChat(userId);

      logger.info(`Guest chat converted to user chat: ${sessionId} -> ${userId}`);
    } catch (error) {
      logger.error('Error converting guest chat:', error);
      throw error;
    }
  }

  /**
   * Get guest session info
   */
  static async getGuestSessionInfo(sessionId: string): Promise<{
    messageCount: number;
    remainingMessages: number;
    hasReachedLimit: boolean;
  }> {
    try {
      const guestSession = await GuestSession.findBySessionId(sessionId);
      const guestLimit = parseInt(process.env.GUEST_CHAT_LIMIT || '5');

      if (!guestSession) {
        return {
          messageCount: 0,
          remainingMessages: guestLimit,
          hasReachedLimit: false,
        };
      }

      return {
        messageCount: guestSession.messageCount,
        remainingMessages: guestSession.getRemainingMessages(guestLimit),
        hasReachedLimit: guestSession.hasReachedLimit(guestLimit),
      };
    } catch (error) {
      logger.error('Error getting guest session info:', error);
      throw error;
    }
  }

  /**
   * Regenerate AI response for a specific message
   */
  static async regenerateResponse(
    messageId: string,
    userId?: string,
    llmModel?: string,
    res?: Response
  ): Promise<{ response: string; messageId: string; version: number; isGuest: boolean }> {
    try {
      // Find the original message
      const originalMessage = await ChatMessage.findById(messageId);
      if (!originalMessage) {
        throw new Error(ERROR_MESSAGES.MESSAGE_NOT_FOUND || 'Message not found');
      }

      // Find the chat or thread to verify ownership and get context
      // First try to find as a Chat (legacy system)
      let chat = await Chat.findByPk(originalMessage.chatId);
      let chatThread = null;
      let isGuest = false;
      let chatOwnerId = null;

      if (chat) {
        // Found in legacy Chat system
        isGuest = chat.isGuest;
        chatOwnerId = chat.userId;
      } else {
        // Try to find as a ChatThread (new system)
        chatThread = await ChatThread.findByPk(originalMessage.chatId);
        if (!chatThread) {
          throw new Error(ERROR_MESSAGES.CHAT_NOT_FOUND);
        }
        isGuest = chatThread.isGuest;
        chatOwnerId = chatThread.userId;
      }

      // Verify ownership for authenticated users
      if (userId && !isGuest && chatOwnerId !== userId) {
        throw new Error(ERROR_MESSAGES.UNAUTHORIZED);
      }

      // Check credits for authenticated users
      if (userId && !isGuest) {
        const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
        if (!hasCredits) {
          throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
        }
      }

      // Build conversation history up to the target message
      const chatId = chat ? chat.id : chatThread!.id;
      const conversationHistory = await this.getChatHistoryUpToMessage(chatId, messageId);

      // Determine system prompt based on user type
      const systemPrompt = isGuest ? this.getGuestSystemPrompt() : this.getSystemPrompt();

      if (res) {
        // Streaming response
        const metadata = {
          originalMessageId: messageId,
          chatId: chatId,
          isGuest: isGuest,
          isRegeneration: true,
          threadId: chatThread ? chatThread.id : undefined,
        };

        const response = await LLMService.generateStreamingResponse(
          originalMessage.message,
          res,
          llmModel || originalMessage.llmModel,
          systemPrompt,
          conversationHistory,
          metadata,
          async (fullResponse: string) => {
            // Create regenerated response
            const regeneratedMessage = await ChatMessage.createRegeneratedResponse({
              originalMessageId: messageId,
              newResponse: fullResponse,
              llmModel: llmModel || originalMessage.llmModel,
            });

            // Deduct credits for authenticated users
            if (userId && !isGuest) {
              await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);
            }

            return {
              messageId: regeneratedMessage.id,
              version: regeneratedMessage.version,
            };
          }
        );

        return {
          response: 'streaming',
          messageId: 'will-be-in-stream',
          version: 0,
          isGuest: isGuest,
        };
      } else {
        // Non-streaming response
        const response = await LLMService.generateResponse(
          originalMessage.message,
          llmModel || originalMessage.llmModel,
          systemPrompt,
          conversationHistory
        );

        // Create regenerated response
        const regeneratedMessage = await ChatMessage.createRegeneratedResponse({
          originalMessageId: messageId,
          newResponse: response,
          llmModel: llmModel || originalMessage.llmModel,
        });

        // Deduct credits for authenticated users
        if (userId && !isGuest) {
          await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);
        }

        logger.info(`Response regenerated for message: ${messageId}, new version: ${regeneratedMessage.version}`);

        return {
          response,
          messageId: regeneratedMessage.id,
          version: regeneratedMessage.version,
          isGuest: isGuest,
        };
      }
    } catch (error) {
      logger.error('Error regenerating response:', error);
      throw error;
    }
  }

  /**
   * Generate chat title from first message
   */
  private static generateChatTitle(message: string): string {
    const maxLength = 50;
    const title = message.trim();

    if (title.length <= maxLength) {
      return title;
    }

    return title.substring(0, maxLength - 3) + '...';
  }

  /**
   * Get system prompt for regular users
   */
  private static getSystemPrompt(): string {
    return `You are a helpful AI assistant. Provide accurate, helpful, and concise responses to user questions.
    Be friendly and professional in your interactions. If you're unsure about something, acknowledge it honestly.`;
  }

  /**
   * Get system prompt for guest users
   */
  private static getGuestSystemPrompt(): string {
    return `You are a helpful AI assistant. Provide accurate, helpful, and concise responses to user questions.
    Be friendly and professional in your interactions. This is a guest session with limited interactions.
    If you're unsure about something, acknowledge it honestly.`;
  }

  /**
   * Clean up old guest sessions
   */
  static async cleanupOldGuestSessions(daysOld: number = 7): Promise<number> {
    try {
      const deletedCount = await GuestSession.cleanupOldSessions(daysOld);
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} old guest sessions`);
      }
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old guest sessions:', error);
      return 0;
    }
  }

  /**
   * Get chat statistics
   */
  static async getChatStatistics(): Promise<{
    totalChats: number;
    userChats: number;
    guestChats: number;
    totalMessages: number;
    guestSessionStats: any;
  }> {
    try {
      const totalChats = await Chat.count();
      const userChats = await Chat.count({ where: { isGuest: false } });
      const guestChats = await Chat.count({ where: { isGuest: true } });
      const totalMessages = await ChatMessage.count();
      const guestSessionStats = await GuestSession.getSessionStats();

      return {
        totalChats,
        userChats,
        guestChats,
        totalMessages,
        guestSessionStats,
      };
    } catch (error) {
      logger.error('Error getting chat statistics:', error);
      throw error;
    }
  }

  /**
   * Simplified streaming chat for authenticated users with file attachment support
   */
  static async processSimpleUserChatStreamingWithAttachment(
    userId: string,
    chatRequest: SimpleChatRequest,
    res: Response,
    attachedFile?: ProcessedFile
  ): Promise<SimpleChatResponse> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chatThread: ChatThread;

      if (sessionId) {
        // Find existing thread by sessionId
        const existingThread = await ChatThread.findBySessionId(sessionId);
        if (existingThread && existingThread.userId === userId) {
          chatThread = existingThread;
        } else {
          // Create new thread with provided sessionId and initial name
          const initialName = ChatThread.prototype.generateThreadName(message);
          chatThread = await ChatThread.createThread({
            userId,
            sessionId,
            name: initialName,
            isGuest: false,
          });
        }
      } else {
        // Create new thread with generated session ID and initial name
        const newSessionId = EncryptionUtil.generateSessionId();
        const initialName = ChatThread.prototype.generateThreadName(message || 'File Analysis');
        chatThread = await ChatThread.createThread({
          userId,
          sessionId: newSessionId,
          name: initialName,
          isGuest: false,
        });
      }

      // Generate streaming response using LLM with file attachment
      const conversationHistory = await this.getChatHistory(chatThread.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chatThread.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: false,
        userId: userId,
        threadId: chatThread.id,
        hasAttachment: !!attachedFile
      };

      const response = await LLMService.generateStreamingResponse(
        message || 'Please analyze the attached file.',
        res,
        llmModel || 'gpt-3.5-turbo',
        this.getSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response with attachment info
          const chatMessage = await ChatMessage.createMessage({
            chatId: chatThread.id, // Using thread ID as chat ID
            message: message || 'Please analyze the attached file.',
            response: fullResponse,
            llmModel: llmModel || 'gpt-3.5-turbo',
            attachmentPath: attachedFile?.filePath,
            attachmentName: attachedFile?.metadata.originalName,
            attachmentType: attachedFile?.metadata.mimeType,
            attachmentSize: attachedFile?.metadata.size,
          });

          // Deduct 1 credit for the chat message
          await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE);

          // Update thread timestamp
          chatThread.changed('updatedAt', true);
          await chatThread.save();

          return {
            messageId: chatMessage.id
          };
        },
        attachedFile
      );

      logger.info(`Simple streaming chat with attachment processed for user: ${userId}, thread: ${chatThread.id}`);

      return {
        sessionId: chatThread.sessionId,
        messageId: 'will-be-in-stream',
        isGuest: false,
        userId: userId,
      };
    } catch (error) {
      logger.error('Error processing simple user streaming chat with attachment:', error);
      throw error;
    }
  }

  /**
   * Simplified streaming chat for guest users with file attachment support
   */
  static async processSimpleGuestChatStreamingWithAttachment(
    chatRequest: SimpleChatRequest,
    res: Response,
    attachedFile?: ProcessedFile
  ): Promise<SimpleChatResponse> {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      // Generate session ID if not provided
      const finalSessionId = sessionId || EncryptionUtil.generateSessionId();

      // Find or create guest thread
      let chatThread = await ChatThread.findBySessionId(finalSessionId);
      if (!chatThread) {
        // Create new guest thread with initial name
        const initialName = ChatThread.prototype.generateThreadName(message || 'File Analysis');
        chatThread = await ChatThread.createThread({
          sessionId: finalSessionId,
          name: initialName,
          isGuest: true,
        });
      }

      // Generate streaming response using LLM with file attachment
      const conversationHistory = await this.getChatHistory(chatThread.id);

      // Prepare metadata to send with stream
      const metadata = {
        sessionId: chatThread.sessionId,
        messageId: null, // Will be set after message is saved
        isGuest: true,
        threadId: chatThread.id,
        hasAttachment: !!attachedFile
      };

      const response = await LLMService.generateStreamingResponse(
        message || 'Please analyze the attached file.',
        res,
        llmModel || 'gpt-3.5-turbo',
        this.getGuestSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse: string) => {
          // Save message and response with attachment info
          const chatMessage = await ChatMessage.createMessage({
            chatId: chatThread.id, // Using thread ID as chat ID
            message: message || 'Please analyze the attached file.',
            response: fullResponse,
            llmModel: llmModel || 'gpt-3.5-turbo',
            attachmentPath: attachedFile?.filePath,
            attachmentName: attachedFile?.metadata.originalName,
            attachmentType: attachedFile?.metadata.mimeType,
            attachmentSize: attachedFile?.metadata.size,
          });

          return {
            messageId: chatMessage.id
          };
        },
        attachedFile
      );

      logger.info(`Simple guest streaming chat with attachment processed: ${finalSessionId}`);

      return {
        sessionId: chatThread.sessionId,
        messageId: 'will-be-in-stream',
        isGuest: true,
      };
    } catch (error) {
      logger.error('Error processing simple guest streaming chat with attachment:', error);
      throw error;
    }
  }
}
