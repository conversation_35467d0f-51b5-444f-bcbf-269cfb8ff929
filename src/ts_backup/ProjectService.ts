import { Project, ChatThread, ChatMessage } from '../models/chat';
import { PineconeService } from './PineconeService';
import logger from '../config/logger';

export interface CreateProjectRequest {
  name: string;
  description: string;
  rules: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  rules?: string;
}

export class ProjectService {
  /**
   * Create a new project
   */
  static async createProject(userId: string, projectData: CreateProjectRequest): Promise<Project> {
    try {
      const { name, description, rules } = projectData;

      if (!name || name.trim().length === 0) {
        throw new Error('Project name is required');
      }

      if (!description || description.trim().length === 0) {
        throw new Error('Project description is required');
      }

      if (!rules || rules.trim().length === 0) {
        throw new Error('Project rules are required');
      }

      const project = await Project.createProject({
        userId,
        name: name.trim(),
        description: description.trim(),
        rules: rules.trim(),
      });

      logger.info(`Project created: ${project.id} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error creating project:', error);
      throw error;
    }
  }

  /**
   * Get user projects with pagination
   */
  static async getUserProjects(userId: string, limit: number = 6, offset: number = 0): Promise<Project[]> {
    try {
      return Project.findUserProjects(userId, limit, offset);
    } catch (error) {
      logger.error('Error getting user projects:', error);
      throw error;
    }
  }

  /**
   * Get project by ID
   */
  static async getProject(projectId: string, userId: string): Promise<Project | null> {
    try {
      return Project.findByIdAndUser(projectId, userId);
    } catch (error) {
      logger.error('Error getting project:', error);
      throw error;
    }
  }

  /**
   * Update project
   */
  static async updateProject(
    projectId: string,
    userId: string,
    updateData: UpdateProjectRequest
  ): Promise<Project> {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Validate update data
      if (updateData.name !== undefined && updateData.name.trim().length === 0) {
        throw new Error('Project name cannot be empty');
      }

      if (updateData.description !== undefined && updateData.description.trim().length === 0) {
        throw new Error('Project description cannot be empty');
      }

      if (updateData.rules !== undefined && updateData.rules.trim().length === 0) {
        throw new Error('Project rules cannot be empty');
      }

      // Trim strings if provided
      const trimmedData = {
        name: updateData.name?.trim(),
        description: updateData.description?.trim(),
        rules: updateData.rules?.trim(),
      };

      await project.updateProject(trimmedData);

      logger.info(`Project updated: ${projectId} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error updating project:', error);
      throw error;
    }
  }

  /**
   * Delete project and all associated data
   */
  static async deleteProject(projectId: string, userId: string): Promise<void> {
    try {
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Delete all project threads
      const projectThreads = await ChatThread.findProjectThreads(projectId);
      for (const thread of projectThreads) {
        // Delete Pinecone data for each thread
        await PineconeService.deleteThreadMessages(projectId, thread.id);
      }

      // Delete project threads from database
      await ChatThread.destroy({
        where: { projectId }
      });

      // Delete the project
      await project.destroy();

      logger.info(`Project deleted: ${projectId} by user ${userId}`);
    } catch (error) {
      logger.error('Error deleting project:', error);
      throw error;
    }
  }

  /**
   * Get project threads with pagination
   */
  static async getProjectThreads(
    projectId: string,
    userId: string,
    limit: number = 6,
    offset: number = 0
  ): Promise<ChatThread[]> {
    try {
      // Verify project belongs to user
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      return ChatThread.findProjectThreads(projectId, limit, offset);
    } catch (error) {
      logger.error('Error getting project threads:', error);
      throw error;
    }
  }

  /**
   * Get project statistics
   */
  static async getProjectStats(projectId: string, userId: string): Promise<{
    threadCount: number;
    messageCount: number;
    lastActivity: Date | null;
  }> {
    try {
      // Verify project belongs to user
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Get thread count
      const threadCount = await ChatThread.count({
        where: { projectId }
      });

      // Get message count across all project threads
      const threads = await ChatThread.findAll({
        where: { projectId },
        attributes: ['id']
      });

      let messageCount = 0;
      for (const thread of threads) {
        const count = await ChatMessage.count({
          where: { chatId: thread.id }
        });
        messageCount += count;
      }

      // Get last activity
      const lastThread = await ChatThread.findOne({
        where: { projectId },
        order: [['updatedAt', 'DESC']],
        attributes: ['updatedAt']
      });

      return {
        threadCount,
        messageCount,
        lastActivity: lastThread?.updatedAt || null,
      };
    } catch (error) {
      logger.error('Error getting project stats:', error);
      throw error;
    }
  }

  /**
   * Search projects by name
   */
  static async searchProjects(
    userId: string,
    searchTerm: string,
    limit: number = 6,
    offset: number = 0
  ): Promise<Project[]> {
    try {
      const { Op } = await import('sequelize');

      return Project.findAll({
        where: {
          userId,
          name: {
            [Op.like]: `%${searchTerm}%`
          }
        },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error searching projects:', error);
      throw error;
    }
  }
}
