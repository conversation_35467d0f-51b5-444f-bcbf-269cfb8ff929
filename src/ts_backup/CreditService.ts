import { UserCredit, UserCreditTransaction } from '../models/user';
import { CREDIT_SYSTEM } from '../utils/constants';
import logger from '../config/logger';

export class CreditService {
  /**
   * Initialize user credits (called when user is first verified)
   */
  static async initializeUserCredits(userId: string, initialCredits: number = CREDIT_SYSTEM.INITIAL_CREDITS): Promise<UserCredit> {
    try {
      // Check if user already has credits
      const existingCredit = await UserCredit.findByUserId(userId);
      if (existingCredit) {
        logger.info(`User ${userId} already has credits: ${existingCredit.credits}`);
        return existingCredit;
      }

      // Create new credit record
      const userCredit = await UserCredit.createUserCredit(userId, initialCredits);

      // Create transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount: initialCredits,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT,
        description: CREDIT_SYSTEM.DESCRIPTIONS.INITIAL_SIGNUP,
      });

      logger.info(`Initialized ${initialCredits} credits for user ${userId}`);
      return userCredit;
    } catch (error) {
      logger.error('Error initializing user credits:', error);
      throw error;
    }
  }

  /**
   * Get user's current credit balance
   */
  static async getUserCredits(userId: string): Promise<number> {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      return userCredit ? userCredit.credits : 0;
    } catch (error) {
      logger.error('Error getting user credits:', error);
      throw error;
    }
  }

  /**
   * Check if user has enough credits
   */
  static async hasEnoughCredits(userId: string, requiredCredits: number): Promise<boolean> {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      return userCredit ? userCredit.hasEnoughCredits(requiredCredits) : false;
    } catch (error) {
      logger.error('Error checking user credits:', error);
      throw error;
    }
  }

  /**
   * Deduct credits from user account (for chat messages)
   */
  static async deductCredits(
    userId: string,
    amount: number,
    description: string = CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
  ): Promise<boolean> {
    try {
      const userCredit = await UserCredit.findByUserId(userId);
      if (!userCredit) {
        throw new Error('User credit record not found');
      }

      // Check if user has enough credits
      if (!userCredit.hasEnoughCredits(amount)) {
        logger.warn(`User ${userId} has insufficient credits. Required: ${amount}, Available: ${userCredit.credits}`);
        return false;
      }

      // Deduct credits
      const success = await userCredit.deductCredits(amount);
      if (!success) {
        return false;
      }

      // Create transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.DEBIT,
        description,
      });

      logger.info(`Deducted ${amount} credits from user ${userId}. Remaining: ${userCredit.credits}`);
      return true;
    } catch (error) {
      logger.error('Error deducting user credits:', error);
      throw error;
    }
  }

  /**
   * Add credits to user account
   */
  static async addCredits(
    userId: string,
    amount: number,
    description: string = 'Credit added'
  ): Promise<void> {
    try {
      const [userCredit] = await UserCredit.findOrCreateByUserId(userId, 0);

      // Add credits
      await userCredit.addCredits(amount);

      // Create transaction record
      await UserCreditTransaction.createTransaction({
        userId,
        amount,
        type: CREDIT_SYSTEM.TRANSACTION_TYPES.CREDIT,
        description,
      });

      logger.info(`Added ${amount} credits to user ${userId}. New balance: ${userCredit.credits}`);
    } catch (error) {
      logger.error('Error adding user credits:', error);
      throw error;
    }
  }

  /**
   * Get user's credit transaction history
   */
  static async getCreditTransactions(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{
    transactions: UserCreditTransaction[];
    total: number;
  }> {
    try {
      const [transactions, total] = await Promise.all([
        UserCreditTransaction.getUserTransactions(userId, limit, offset),
        UserCreditTransaction.getUserTransactionCount(userId),
      ]);

      return { transactions, total };
    } catch (error) {
      logger.error('Error getting credit transactions:', error);
      throw error;
    }
  }

  /**
   * Get user's credit summary
   */
  static async getCreditSummary(userId: string): Promise<{
    currentCredits: number;
    totalEarned: number;
    totalSpent: number;
    transactionCount: number;
  }> {
    try {
      const [userCredit, creditTransactions, debitTransactions] = await Promise.all([
        UserCredit.findByUserId(userId),
        UserCreditTransaction.getTransactionsByType(userId, 'CREDIT'),
        UserCreditTransaction.getTransactionsByType(userId, 'DEBIT'),
      ]);

      const currentCredits = userCredit ? userCredit.credits : 0;
      const totalEarned = creditTransactions.reduce((sum, tx) => sum + tx.amount, 0);
      const totalSpent = debitTransactions.reduce((sum, tx) => sum + tx.amount, 0);
      const transactionCount = await UserCreditTransaction.getUserTransactionCount(userId);

      return {
        currentCredits,
        totalEarned,
        totalSpent,
        transactionCount,
      };
    } catch (error) {
      logger.error('Error getting credit summary:', error);
      throw error;
    }
  }
}
