import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import authRoutes from './auth';
import chatRoutes from './chat';
import userRoutes from './user';
import threadRoutes from './threads';
import projectRoutes from './projects';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  ResponseUtil.success(res, 'Server is healthy', {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API version info
router.get('/version', (req, res) => {
  ResponseUtil.success(res, 'API version information', {
    version: '1.0.0',
    name: 'The Infini AI Backend',
    description: 'AI Chat Backend with Node.js, TypeScript, and Langchain',
    author: 'The Infini Team',
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/chat', chatRoutes);
router.use('/user', userRoutes);
router.use('/threads', threadRoutes);
router.use('/projects', projectRoutes);

export default router;
