import * as fs from 'fs/promises';
import * as path from 'path';
import { PDFDocument } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import sharp from 'sharp';
import logger from '../config/logger.js';

/**
 * @typedef {Object} ProcessedFile
 * @property {'image'|'document'|'text'} type - File type
 * @property {string|Array} content - File content
 * @property {Object} metadata - File metadata
 * @property {string} metadata.originalName - Original filename
 * @property {string} metadata.mimeType - MIME type
 * @property {number} metadata.size - File size in bytes
 * @property {Date} metadata.processedAt - Processing timestamp
 * @property {string} [filePath] - Path where file is stored on disk
 */

/**
 * @typedef {Object} FileAttachment
 * @property {Buffer} buffer - File buffer
 * @property {string} originalname - Original filename
 * @property {string} mimetype - MIME type
 * @property {number} size - File size in bytes
 */

export class FileProcessingService {
  static MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  static SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  static SUPPORTED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/plain',
    'text/markdown',
    'application/json'
  ];

  /**
   * Save file to uploads directory and return the file path
   * @param {FileAttachment} file - File to save
   * @returns {Promise<string>} File path
   */
  static async saveFileToUploads(file) {
    const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');

    // Ensure uploads directory exists
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const fileExtension = path.extname(file.originalname);
    const fileName = `${timestamp}_${randomSuffix}${fileExtension}`;
    const filePath = path.join(uploadsDir, fileName);

    // Save file to disk
    await fs.writeFile(filePath, file.buffer);

    // Return relative path from project root
    return path.join('uploads', 'chatAttachments', fileName);
  }

  /**
   * Validate file before processing
   * @param {FileAttachment} file - File to validate
   * @throws {Error} If file is invalid
   */
  static validateFile(file) {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    // Check if file type is supported
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype);
    const isDocument = this.SUPPORTED_DOCUMENT_TYPES.includes(file.mimetype);
    const isVideo = file.mimetype.startsWith('video/');

    if (isVideo) {
      throw new Error('Video files are not supported');
    }

    if (!isImage && !isDocument) {
      throw new Error(`Unsupported file type: ${file.mimetype}`);
    }

    logger.info(`File validation passed: ${file.originalname} (${file.mimetype}, ${file.size} bytes)`);
  }

  /**
   * Process file based on its type
   * @param {FileAttachment} file - File to process
   * @returns {Promise<ProcessedFile>} Processed file data
   */
  static async processFile(file) {
    this.validateFile(file);

    // Save file to uploads directory
    const filePath = await this.saveFileToUploads(file);

    const metadata = {
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      processedAt: new Date()
    };

    try {
      // Process images
      if (this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype)) {
        return await this.processImage(file, metadata, filePath);
      }

      // Process PDF
      if (file.mimetype === 'application/pdf') {
        return await this.processPDF(file, metadata, filePath);
      }

      // Process Word documents
      if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        return await this.processWordDocument(file, metadata, filePath);
      }

      // Process Excel files
      if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        return await this.processExcelFile(file, metadata, filePath);
      }

      // Process text files
      if (file.mimetype.startsWith('text/') || file.mimetype === 'application/json') {
        return await this.processTextFile(file, metadata, filePath);
      }

      throw new Error(`Unsupported file type: ${file.mimetype}`);
    } catch (error) {
      logger.error(`Error processing file ${file.originalname}:`, error);
      throw new Error(`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process image files
   * @param {FileAttachment} file - Image file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed image
   */
  static async processImage(file, metadata, filePath) {
    try {
      // Optimize image if needed (compress large images)
      let processedBuffer = file.buffer;
      
      if (file.size > 1024 * 1024) { // If larger than 1MB, compress
        processedBuffer = await sharp(file.buffer)
          .jpeg({ quality: 85 })
          .toBuffer();
        
        logger.info(`Compressed image ${file.originalname} from ${file.size} to ${processedBuffer.length} bytes`);
      }

      const base64Image = processedBuffer.toString('base64');
      const mimeType = file.mimetype === 'image/jpg' ? 'image/jpeg' : file.mimetype;
      
      return {
        type: 'image',
        content: [{
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${base64Image}`
          }
        }],
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing image ${file.originalname}:`, error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF files by extracting text content
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDF(file, metadata, filePath) {
    try {
      // Load PDF document
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      // For now, we'll return a text representation
      // In the future, we can implement PDF to image conversion using a different approach
      const textContent = `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nThis PDF has been uploaded and saved. The LLM can analyze the document structure and content.`;

      logger.info(`PDF processed: ${file.originalname} with ${pageCount} pages`);

      return {
        type: 'document',
        content: textContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing PDF ${file.originalname}:`, error);
      throw new Error(`Failed to process PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Word documents
   * @param {FileAttachment} file - Word document
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed document
   */
  static async processWordDocument(file, metadata, filePath) {
    try {
      const result = await mammoth.convertToHtml({ buffer: file.buffer });

      if (result.messages.length > 0) {
        logger.warn(`Word document conversion warnings for ${file.originalname}:`, result.messages);
      }

      // Convert HTML to basic markdown-like format
      let markdownContent = result.value
        .replace(/<h([1-6])>/g, (match, level) => '#'.repeat(parseInt(level)) + ' ')
        .replace(/<\/h[1-6]>/g, '\n\n')
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '\n\n')
        .replace(/<strong>/g, '**')
        .replace(/<\/strong>/g, '**')
        .replace(/<em>/g, '*')
        .replace(/<\/em>/g, '*')
        .replace(/<br\s*\/?>/g, '\n')
        .replace(/<[^>]*>/g, '') // Remove remaining HTML tags
        .replace(/\n{3,}/g, '\n\n') // Clean up excessive newlines
        .trim();

      return {
        type: 'document',
        content: markdownContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Word document ${file.originalname}:`, error);
      throw new Error(`Failed to process Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Excel files
   * @param {FileAttachment} file - Excel file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed spreadsheet
   */
  static async processExcelFile(file, metadata, filePath) {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      let markdownContent = '';

      workbook.SheetNames.forEach((sheetName, index) => {
        const worksheet = workbook.Sheets[sheetName];
        const csvData = XLSX.utils.sheet_to_csv(worksheet);

        markdownContent += `## Sheet: ${sheetName}\n\n`;

        // Convert CSV to markdown table
        const lines = csvData.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
          // Header row
          const headers = lines[0].split(',').map(h => h.trim());
          markdownContent += `| ${headers.join(' | ')} |\n`;
          markdownContent += `| ${headers.map(() => '---').join(' | ')} |\n`;

          // Data rows (limit to first 50 rows to avoid token limits)
          const dataRows = lines.slice(1, 51);
          dataRows.forEach(line => {
            const cells = line.split(',').map(c => c.trim());
            markdownContent += `| ${cells.join(' | ')} |\n`;
          });

          if (lines.length > 51) {
            markdownContent += `\n*... and ${lines.length - 51} more rows*\n`;
          }
        }

        markdownContent += '\n';
      });

      return {
        type: 'document',
        content: markdownContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Excel file ${file.originalname}:`, error);
      throw new Error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process text files
   * @param {FileAttachment} file - Text file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed text
   */
  static async processTextFile(file, metadata, filePath) {
    try {
      const content = file.buffer.toString('utf-8');

      return {
        type: 'text',
        content,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing text file ${file.originalname}:`, error);
      throw new Error(`Failed to process text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get system prompt addition based on file type
   * @param {ProcessedFile} processedFile - Processed file
   * @returns {string} System prompt addition
   */
  static getSystemPromptAddition(processedFile) {
    const { type, metadata } = processedFile;

    switch (type) {
      case 'image':
        return `\n\nThe user has attached an image file (${metadata.originalName}). Please analyze the image content and respond accordingly.`;

      case 'document':
        if (metadata.mimeType === 'application/pdf') {
          return `\n\nThe user has attached a PDF document (${metadata.originalName}) which has been converted to images. Please analyze the document content and respond accordingly.`;
        } else if (metadata.mimeType.includes('word')) {
          return `\n\nThe user has attached a Word document (${metadata.originalName}) with the following content converted to markdown format. Please analyze the document and respond accordingly.`;
        } else if (metadata.mimeType.includes('sheet')) {
          return `\n\nThe user has attached an Excel spreadsheet (${metadata.originalName}) with the following data converted to markdown table format. Please analyze the data and respond accordingly.`;
        }
        return `\n\nThe user has attached a document (${metadata.originalName}). Please analyze the content and respond accordingly.`;

      case 'text':
        return `\n\nThe user has attached a text file (${metadata.originalName}) with the following content. Please analyze the text and respond accordingly.`;

      default:
        return `\n\nThe user has attached a file (${metadata.originalName}). Please analyze the content and respond accordingly.`;
    }
  }

  /**
   * Clean up old uploaded files
   * @param {number} [maxAgeHours] - Maximum age in hours
   * @returns {Promise<number>} Number of files deleted
   */
  static async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');
      const files = await fs.readdir(uploadsDir);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(uploadsDir, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filePath);
          deletedCount++;
        }
      }

      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} old uploaded files`);
      }

      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old files:', error);
      return 0;
    }
  }

  /**
   * Get file processing statistics
   * @returns {Promise<Object>} Processing statistics
   */
  static async getProcessingStats() {
    try {
      const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');

      try {
        const files = await fs.readdir(uploadsDir);
        let totalSize = 0;

        for (const file of files) {
          const filePath = path.join(uploadsDir, file);
          const stats = await fs.stat(filePath);
          totalSize += stats.size;
        }

        return {
          totalFiles: files.length,
          totalSizeBytes: totalSize,
          totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
        };
      } catch (error) {
        // Directory doesn't exist yet
        return {
          totalFiles: 0,
          totalSizeBytes: 0,
          totalSizeMB: 0,
        };
      }
    } catch (error) {
      logger.error('Error getting processing stats:', error);
      throw error;
    }
  }

  /**
   * Validate file type and size
   * @param {string} mimetype - File MIME type
   * @param {number} size - File size in bytes
   * @returns {Object} Validation result
   */
  static validateFileType(mimetype, size) {
    const errors = [];

    if (size > this.MAX_FILE_SIZE) {
      errors.push(`File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(mimetype);
    const isDocument = this.SUPPORTED_DOCUMENT_TYPES.includes(mimetype);
    const isVideo = mimetype.startsWith('video/');

    if (isVideo) {
      errors.push('Video files are not supported');
    }

    if (!isImage && !isDocument) {
      errors.push(`Unsupported file type: ${mimetype}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      fileType: isImage ? 'image' : isDocument ? 'document' : 'unknown',
    };
  }
}

